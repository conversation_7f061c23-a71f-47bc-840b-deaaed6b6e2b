<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Beatleaf - Modern Music Collaboration</title>

    <!-- Safari Theme Color -->
    <meta name="theme-color" content="#000000" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />

    <!-- Favicon -->
    <link
      rel="icon"
      type="image/x-icon"
      href="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//BeatleafLogoRound.ico"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, sans-serif;
        line-height: 1.6;
        color: #ffffff;
        background: radial-gradient(
            circle at 30% 30%,
            #6257d255 0%,
            transparent 70%
          ),
          radial-gradient(circle at 80% 60%, #28839a4d 0%, transparent 60%),
          linear-gradient(140deg, #000000 0%, #000000 100%);
        overflow-x: hidden;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Header */
      header {
        background: rgba(255, 255, 255, 0);
        backdrop-filter: blur(10px);
        padding: 20px 0;
        position: fixed;
        width: 100%;
        top: 0;
        z-index: 1000;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background 300ms ease-in-out,
          backdrop-filter 300ms ease-in-out;
      }

      .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        height: 32px;
        transition: opacity 0.3s ease;
      }

      .logo:hover {
        opacity: 0.8;
      }

      .nav-links {
        display: flex;
        list-style: none;
        gap: 30px;
      }

      .nav-links a {
        color: white;
        text-decoration: none;
        transition: opacity 0.3s ease;
      }

      .nav-links a:hover {
        opacity: 0.8;
      }

      /* Hero Section */
      .hero {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding-top: 100px;
        position: relative;
      }

      .hero-content h1 {
        font-size: 4rem;
        font-weight: 900;
        color: white;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        animation: fadeInUp 1s ease-out;
      }

      .hero-content p {
        font-size: 1.5rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 40px;
        animation: fadeInUp 1s ease-out 0.2s both;
      }

      .cta-button {
        display: inline-block;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 18px 40px;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        animation: fadeInUp 1s ease-out 0.4s both;
      }

      .cta-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
      }

      /* Features Section */
      .features {
        padding: 100px 0;
      }

      .feature {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 60px;
        align-items: center;
        margin-bottom: 120px;
        opacity: 0;
      }

      .feature:nth-child(even) {
        direction: rtl;
      }

      .feature:nth-child(even) > * {
        direction: ltr;
      }

      .feature-content h2 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .feature-content p {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.8;
      }

      .feature-visual {
        height: 400px;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        position: relative;
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      .feature-visual img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 20px;
        transition: transform 0.3s ease;
      }

      .feature-visual:hover img {
        transform: scale(1.05);
      }

      /* Benefits Section */
      .benefits {
        padding: 100px 0;
      }

      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        margin-top: 60px;
      }

      .benefit-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        padding: 40px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
        opacity: 0;
      }

      .benefit-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(102, 126, 234, 0.1),
          rgba(118, 75, 162, 0.1)
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }

      .benefit-card:hover {
        transform: translateY(-15px) scale(1.02);
        border-color: rgba(102, 126, 234, 0.4);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4),
          0 0 30px rgba(102, 126, 234, 0.2);
        background: rgba(255, 255, 255, 0.08);
      }

      .benefit-card:hover::before {
        opacity: 1;
      }

      .benefit-card h3 {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 15px;
        color: #ffffff;
        transition: all 0.3s ease;
      }

      .benefit-card:hover h3 {
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transform: translateY(-2px);
      }

      .benefit-card p {
        color: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
      }

      .benefit-card:hover p {
        color: rgba(255, 255, 255, 0.95);
      }

      .benefit-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        display: block;
        transition: all 0.4s ease;
        filter: grayscale(0.3);
      }

      .benefit-card:hover .benefit-icon {
        transform: scale(1.1) rotate(5deg);
        filter: grayscale(0) brightness(1.2);
      }

      .section-title {
        text-align: center;
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      /* CTA Section */
      .cta-section {
        padding: 100px 0;
        text-align: center;
        color: white;
      }

      .cta-section h2 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 20px;
      }

      .cta-section p {
        font-size: 1.3rem;
        margin-bottom: 40px;
        opacity: 0.9;
      }

      /* Footer */
      footer {
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        padding: 40px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .footer-logo {
        height: 24px;
        margin-bottom: 10px;
        opacity: 0.8;
      }

      /* Animations */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Responsive */
      @media (max-width: 768px) {
        .hero-content h1 {
          font-size: 2.5rem;
        }

        .hero-content p {
          font-size: 1.2rem;
        }

        .feature {
          grid-template-columns: 1fr;
          gap: 40px;
        }

        .feature:nth-child(even) {
          direction: ltr;
        }

        .feature-content h2 {
          font-size: 2rem;
        }

        .feature-visual {
          height: 250px;
          margin: 0 auto;
          max-width: 100%;
        }

        .section-title {
          font-size: 2rem;
        }

        .nav-links {
          display: none;
        }

        .benefits-grid {
          grid-template-columns: 1fr;
          gap: 30px;
        }

        .benefit-card {
          padding: 30px 20px;
        }

        .cta-section h2 {
          font-size: 2rem;
        }

        .cta-section p {
          font-size: 1.1rem;
        }
      }

      /* Extra small screens */
      @media (max-width: 480px) {
        .container {
          padding: 0 15px;
        }

        .hero-content h1 {
          font-size: 2rem;
        }

        .hero-content p {
          font-size: 1rem;
        }

        .feature-visual {
          height: 200px;
        }

        .feature-content h2 {
          font-size: 1.5rem;
        }

        .feature-content p {
          font-size: 1rem;
        }

        .cta-button {
          padding: 15px 30px;
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="container">
        <div class="nav-container">
          <img
            src="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//logo_full.png"
            alt="Beatleaf"
            class="logo"
          />
          <nav>
            <ul class="nav-links">
              <li><a href="#features">Features</a></li>
              <li><a href="#beta">Beta Access</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </header>

    <section class="hero">
      <div class="container">
        <div class="hero-content">
          <h1>Modern Music Collaboration.</h1>
          <p>Built for producers and artists.</p>
          <a href="#features" class="cta-button">What We'll Offer</a>
        </div>
      </div>
    </section>

    <section class="features" id="features">
      <div class="container">
        <div class="feature">
          <div class="feature-content">
            <h2>All files. One place.</h2>
            <p>
              Consolidate stems, files, and notes into tidy projects so your
              workflow never gets interrupted.
            </p>
          </div>
          <div class="feature-visual">
            <img
              src="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//files.png"
              alt="Files organization interface"
            />
          </div>
        </div>

        <div class="feature">
          <div class="feature-content">
            <h2>Effortless versioning.</h2>
            <p>
              Track every edit and update, never lose a great take, and
              instantly roll back to earlier versions.
            </p>
          </div>
          <div class="feature-visual">
            <img
              src="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//version.png"
              alt="Version control interface"
            />
          </div>
        </div>

        <div class="feature">
          <div class="feature-content">
            <h2>Real team collaboration.</h2>
            <p>
              Connect with new collaborators, share projects, feedback, and
              tasks in one space—network and create together, all in sync.
            </p>
          </div>
          <div class="feature-visual">
            <img
              src="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//comments.png"
              alt="Collaboration comments interface"
            />
          </div>
        </div>

        <div class="feature">
          <div class="feature-content">
            <h2>Build your network.</h2>
            <p>
              Easily find new music producers and artists to collaborate
              with—and grow your network and exposure in the process.
            </p>
          </div>
          <div class="feature-visual">
            <img
              src="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//profile.png"
              alt="User profile interface"
            />
          </div>
        </div>
      </div>
    </section>

    <section class="benefits" id="benefits">
      <div class="container">
        <h2 class="section-title">Why Choose Beatleaf?</h2>
        <div class="benefits-grid">
          <div class="benefit-card">
            <span class="benefit-icon">⚡</span>
            <h3>Quick sharing</h3>
            <p>
              Share your projects instantly with collaborators and get feedback
              in real-time.
            </p>
          </div>
          <div class="benefit-card">
            <span class="benefit-icon">🎯</span>
            <h3>Centralized workflow</h3>
            <p>
              Keep everything organized in one place for maximum productivity.
            </p>
          </div>
          <div class="benefit-card">
            <span class="benefit-icon">🚀</span>
            <h3>Grow together</h3>
            <p>
              Build meaningful connections and expand your creative network.
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="cta-section" id="beta">
      <div class="container">
        <h2>Join our beta.</h2>
        <p>Get a preview with early updates.</p>
        <a href="https://app.beatleaf.io/register" class="cta-button"
          >Get Started</a
        >
      </div>
    </section>

    <footer id="contact">
      <div class="container">
        <img
          src="https://owwwjffjsvwpienrwlds.supabase.co/storage/v1/object/public/website//logo_full.png"
          alt="Beatleaf"
          class="footer-logo"
        />
        <p><EMAIL> | Copyright @ beatleaf 2025</p>
      </div>
    </footer>

    <script>
      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Add scroll effect to header
      window.addEventListener("scroll", () => {
        const header = document.querySelector("header");
        if (window.scrollY > 100) {
          header.style.background = "#020617";
          header.style.backdropFilter = "blur(20px)";
        } else {
          header.style.background = "rgba(255, 255, 255, 0.0)";
          header.style.backdropFilter = "blur(10px)";
        }
      });

      // Intersection Observer for animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.animation = "fadeInUp 0.8s ease-out forwards";
          }
        });
      }, observerOptions);

      // Observe all feature sections
      document.querySelectorAll(".feature, .benefit-card").forEach((el) => {
        observer.observe(el);
      });
    </script>
  </body>
</html>
